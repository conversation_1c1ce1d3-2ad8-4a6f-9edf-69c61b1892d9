"use client";

import React, { useState, useRef, useCallback, useEffect } from 'react';
import ReactCrop, { Crop } from 'react-image-crop';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';

import { Upload, Download, Image as ImageIcon } from 'lucide-react';
import { cn } from '@/lib/utils';
import type { CircleCropProps, CropState } from '@/types/circle-crop';



export default function CircleCrop({
  className,
  texts
}: CircleCropProps) {
  const [imageSrc, setImageSrc] = useState<string>('');
  const [crop, setCrop] = useState<Crop>({
    unit: '%',
    x: 25,
    y: 25,
    width: 50,
    height: 50,
  });


  const [isProcessing, setIsProcessing] = useState(false);
  const [isDragOver, setIsDragOver] = useState(false);
  const [originalFile, setOriginalFile] = useState<File | null>(null);
  const [imageDisplayStyle, setImageDisplayStyle] = useState<React.CSSProperties>({});

  const imgRef = useRef<HTMLImageElement>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const previewCanvasRef = useRef<HTMLCanvasElement>(null);

  // 处理文件选择
  const handleFileChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      processFile(file);
    }
  }, []);

  // 处理拖拽上传
  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);
    
    const files = e.dataTransfer.files;
    if (files.length > 0) {
      processFile(files[0]);
    }
  }, []);

  const handleDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(true);
  }, []);

  const handleDragLeave = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);
  }, []);

  // 处理文件
  const processFile = useCallback((file: File) => {
    if (!file.type.startsWith('image/')) {
      return;
    }

    // 重置所有相关状态
    setImageDisplayStyle({});

    setOriginalFile(file);
    const reader = new FileReader();
    reader.onload = () => {
      setImageSrc(reader.result as string);
    };
    reader.readAsDataURL(file);
  }, []);

  // 图片加载完成后设置初始裁剪区域
  const handleImageLoad = useCallback((e: React.SyntheticEvent<HTMLImageElement>) => {
    const { naturalWidth, naturalHeight } = e.currentTarget;

    // 计算智能显示尺寸
    const containerMaxWidth = 600; // 假设容器最大宽度
    const containerMaxHeight = window.innerHeight - 300; // 动态计算可用高度
    const minDisplaySize = 400; // 最小显示尺寸

    // 计算图片的宽高比
    const aspectRatio = naturalWidth / naturalHeight;

    let displayWidth, displayHeight;

    // 如果图片太小，适当放大
    if (Math.max(naturalWidth, naturalHeight) < minDisplaySize) {
      if (aspectRatio >= 1) {
        // 横图或正方形
        displayWidth = minDisplaySize;
        displayHeight = minDisplaySize / aspectRatio;
      } else {
        // 竖图
        displayHeight = minDisplaySize;
        displayWidth = minDisplaySize * aspectRatio;
      }
    } else {
      // 图片足够大，按容器限制缩放
      if (naturalWidth > containerMaxWidth || naturalHeight > containerMaxHeight) {
        const scaleX = containerMaxWidth / naturalWidth;
        const scaleY = containerMaxHeight / naturalHeight;
        const scale = Math.min(scaleX, scaleY);
        displayWidth = naturalWidth * scale;
        displayHeight = naturalHeight * scale;
      } else {
        // 图片尺寸合适，保持原尺寸
        displayWidth = naturalWidth;
        displayHeight = naturalHeight;
      }
    }

    // 设置图片显示样式
    setImageDisplayStyle({
      width: `${displayWidth}px`,
      height: `${displayHeight}px`,
      maxWidth: '100%',
      maxHeight: 'calc(100vh - 300px)',
      objectFit: 'contain'
    });

    // 使用百分比设置裁剪区域，这样不依赖于显示尺寸
    // 计算正方形裁剪区域，取较小的边作为基准
    const minDimension = Math.min(naturalWidth, naturalHeight);
    const cropSizePercent = 80; // 80% 的最小边

    // 计算居中的裁剪区域（百分比）
    const cropWidthPercent = (minDimension / naturalWidth) * cropSizePercent;
    const cropHeightPercent = (minDimension / naturalHeight) * cropSizePercent;
    const xPercent = (100 - cropWidthPercent) / 2;
    const yPercent = (100 - cropHeightPercent) / 2;

    // 设置百分比裁剪区域（react-image-crop推荐使用百分比）
    const newCrop: Crop = {
      unit: '%',
      x: xPercent,
      y: yPercent,
      width: cropWidthPercent,
      height: cropHeightPercent,
    };

    setCrop(newCrop);
  }, []);

  // 生成圆形裁剪预览（固定尺寸用于预览）
  const generatePreview = useCallback(async () => {
    if (!imgRef.current || !crop || !previewCanvasRef.current) {
      return;
    }

    const canvas = previewCanvasRef.current;
    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    const image = imgRef.current;
    const naturalWidth = image.naturalWidth;
    const naturalHeight = image.naturalHeight;

    // 直接使用百分比坐标计算原始图片坐标
    const originalX = (crop.x / 100) * naturalWidth;
    const originalY = (crop.y / 100) * naturalHeight;
    const originalWidth = (crop.width / 100) * naturalWidth;
    const originalHeight = (crop.height / 100) * naturalHeight;

    console.log('预览坐标转换调试信息:', {
      原始尺寸: { width: naturalWidth, height: naturalHeight },
      百分比坐标: { x: crop.x, y: crop.y, width: crop.width, height: crop.height },
      原始坐标: { x: originalX, y: originalY, width: originalWidth, height: originalHeight }
    });

    // 预览使用固定尺寸，方便用户查看
    const previewSize = 256;

    // 设置画布尺寸为固定预览尺寸
    canvas.width = previewSize;
    canvas.height = previewSize;

    // 清空画布
    ctx.clearRect(0, 0, previewSize, previewSize);

    // 创建圆形裁剪路径
    ctx.beginPath();
    ctx.arc(previewSize / 2, previewSize / 2, previewSize / 2, 0, 2 * Math.PI);
    ctx.clip();

    // 将裁剪区域缩放到预览尺寸
    ctx.drawImage(
      image,
      originalX, originalY, originalWidth, originalHeight,
      0, 0, previewSize, previewSize
    );
  }, [crop]);

  // 当裁剪区域或尺寸改变时更新预览
  useEffect(() => {
    generatePreview();
  }, [generatePreview]);

  // 下载裁剪后的图片（使用实际裁剪尺寸）
  const handleDownload = useCallback(async () => {
    if (!imgRef.current || !crop || !originalFile) return;

    setIsProcessing(true);

    try {
      const image = imgRef.current;
      const naturalWidth = image.naturalWidth;
      const naturalHeight = image.naturalHeight;

      // 计算实际裁剪坐标和尺寸
      const originalX = (crop.x / 100) * naturalWidth;
      const originalY = (crop.y / 100) * naturalHeight;
      const originalWidth = (crop.width / 100) * naturalWidth;
      const originalHeight = (crop.height / 100) * naturalHeight;

      // 使用实际裁剪区域的尺寸（取较小的边保证是正方形）
      const actualCropSize = Math.min(originalWidth, originalHeight);

      console.log('下载坐标转换调试信息:', {
        原始尺寸: { width: naturalWidth, height: naturalHeight },
        百分比坐标: { x: crop.x, y: crop.y, width: crop.width, height: crop.height },
        原始坐标: { x: originalX, y: originalY, width: originalWidth, height: originalHeight },
        实际裁剪尺寸: actualCropSize
      });

      // 创建临时画布用于实际尺寸的绘制
      const tempCanvas = document.createElement('canvas');
      const tempCtx = tempCanvas.getContext('2d');
      if (!tempCtx) return;

      // 设置画布为实际裁剪尺寸
      tempCanvas.width = actualCropSize;
      tempCanvas.height = actualCropSize;

      // 清空画布
      tempCtx.clearRect(0, 0, actualCropSize, actualCropSize);

      // 创建圆形裁剪路径
      tempCtx.beginPath();
      tempCtx.arc(actualCropSize / 2, actualCropSize / 2, actualCropSize / 2, 0, 2 * Math.PI);
      tempCtx.clip();

      // 绘制实际尺寸的图片
      tempCtx.drawImage(
        image,
        originalX, originalY, originalWidth, originalHeight,
        0, 0, actualCropSize, actualCropSize
      );

      // 获取原始文件的格式
      const mimeType = originalFile.type;
      const extension = mimeType.split('/')[1];

      tempCanvas.toBlob((blob) => {
        if (blob) {
          const url = URL.createObjectURL(blob);
          const link = document.createElement('a');
          link.href = url;
          link.download = `circle-crop-${Math.round(actualCropSize)}x${Math.round(actualCropSize)}.${extension}`;
          document.body.appendChild(link);
          link.click();
          document.body.removeChild(link);
          URL.revokeObjectURL(url);
        }
      }, mimeType, 0.9);
    } catch (error) {
      console.error('Download failed:', error);
    } finally {
      setIsProcessing(false);
    }
  }, [crop, originalFile]);

  return (
    <div className={cn("w-full", className)}>
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* 左侧：图片上传和裁剪区域 */}
        <Card className="flex flex-col h-full min-h-[600px]">
          <CardHeader className="flex-shrink-0">
            <CardTitle className="flex items-center gap-2">
              <ImageIcon className="w-5 h-5" />
              {texts.cropArea}
            </CardTitle>
          </CardHeader>
          <CardContent className="flex-1 flex flex-col min-h-0 pt-6 px-6 pb-0">
            <input
              ref={fileInputRef}
              type="file"
              accept="image/*"
              onChange={handleFileChange}
              className="hidden"
            />

            {/* 图片裁剪区域 */}
            <div className="flex-1 flex flex-col min-h-0 mb-4">
              {imageSrc ? (
                <div className="relative w-full h-full flex items-center justify-center">
                  <ReactCrop
                    crop={crop}
                    onChange={(_, percentCrop) => setCrop(percentCrop)}
                    aspect={1}
                    circularCrop={true}
                    className="max-w-full max-h-full"
                  >
                    <img
                      ref={imgRef}
                      src={imageSrc}
                      alt="Crop preview"
                      onLoad={handleImageLoad}
                      className="h-auto"
                      style={imageDisplayStyle}
                    />
                  </ReactCrop>
                </div>
              ) : (
                <div
                  className={cn(
                    "min-h-[400px] border-2 border-dashed rounded-lg p-8 text-center cursor-pointer transition-colors flex flex-col items-center justify-center",
                    isDragOver ? "border-primary bg-primary/5" : "border-muted-foreground/25"
                  )}
                  onClick={() => fileInputRef.current?.click()}
                  onDragOver={handleDragOver}
                  onDragLeave={handleDragLeave}
                  onDrop={handleDrop}
                >
                  <Upload className={cn(
                    "w-12 h-12 mb-4 transition-colors",
                    isDragOver ? "text-primary" : "text-muted-foreground"
                  )} />
                  <p className={cn(
                    "transition-colors text-lg font-medium mb-2",
                    isDragOver ? "text-primary" : "text-muted-foreground"
                  )}>
                    {isDragOver ? texts.dragDropHint : texts.uploadImage}
                  </p>
                  <p className="text-sm text-muted-foreground/70">
                    {texts.supportedFormats}
                  </p>
                </div>
              )}
            </div>

            {/* 重新选择图片按钮 - 固定在底部 */}
            <div className="flex-shrink-0">
              {imageSrc ? (
                <Button
                  variant="outline"
                  onClick={() => fileInputRef.current?.click()}
                  className="w-full flex items-center gap-2"
                >
                  <Upload className="w-4 h-4" />
                  {texts.reselectImage}
                </Button>
              ) : (
                <div className="h-10"></div>
              )}
            </div>
          </CardContent>
        </Card>

        {/* 右侧：预览和下载区域 */}
        <Card className="flex flex-col h-full min-h-[600px]">
          <CardHeader className="flex-shrink-0">
            <CardTitle>{texts.preview}</CardTitle>
          </CardHeader>
          <CardContent className="flex-1 flex flex-col pt-6 px-6 pb-0">
            {/* 预览画布 */}
            <div className="flex-1 flex items-center justify-center min-h-[300px] mb-4">
              {imageSrc ? (
                <div className="relative">
                  <canvas
                    ref={previewCanvasRef}
                    className="border rounded-full shadow-lg max-w-full h-auto"
                    style={{ maxWidth: '256px', maxHeight: '256px' }}
                  />
                </div>
              ) : (
                <div className="w-64 h-64 border-2 border-dashed border-muted-foreground/25 rounded-full flex items-center justify-center">
                  <p className="text-muted-foreground text-sm">{texts.noImageSelected}</p>
                </div>
              )}
            </div>

            {/* 下载按钮 - 固定在底部 */}
            <div className="flex-shrink-0">
              {imageSrc ? (
                <Button
                  onClick={handleDownload}
                  disabled={isProcessing || !imageSrc}
                  className="w-full"
                >
                  <Download className="w-4 h-4 mr-2" />
                  {isProcessing ? texts.processing : texts.download}
                </Button>
              ) : (
                <div className="h-10"></div>
              )}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
