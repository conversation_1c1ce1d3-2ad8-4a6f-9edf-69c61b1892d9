export interface CircleCropTexts {
  title: string;
  uploadImage: string;
  dragDropHint: string;
  supportedFormats: string;
  preview: string;
  downloadOptions: string;
  download: string;
  processing: string;
  noImageSelected: string;
  cropArea: string;
  adjustCrop: string;
  reselectImage: string;
}

export interface CircleCropProps {
  className?: string;
  texts: CircleCropTexts;
}

export interface CropState {
  x: number;
  y: number;
  width: number;
  height: number;
}
